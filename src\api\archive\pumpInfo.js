import request from '@/utils/request'

// 查询站点档案-泵房信息详细
export function getPumpInfo(id) {
  return request({
    url: '/archive/pumpInfo/' + id,
    method: 'get'
  })
}

// 查询站点档案-泵房信息详细
export function getPumpInfoByVn(villageNumber) {
  return request({
    url: '/archive/pumpInfo/v2/' + villageNumber,
    method: 'get'
  })
}

// 新增站点档案-泵房信息
export function addPumpInfo(data) {
  return request({
    url: '/archive/pumpInfo',
    method: 'post',
    data: data
  })
}

// 修改站点档案-泵房信息
export function updatePumpInfo(data) {
  return request({
    url: '/archive/pumpInfo',
    method: 'put',
    data: data
  })
}

// 查询站点档案-水箱信息详细
export function getWaterBoxInfoByVn(villageNumber) {
  return request({
    url: '/archive/waterbox/v2/' + villageNumber,
    method: 'get'
  })
}

// 新增站点档案-水箱信息
export function addWaterBoxInfo(data) {
  return request({
    url: '/archive/waterbox',
    method: 'post',
    data: data
  })
}

// 修改站点档案-水箱信息
export function updateWaterBoxInfo(data) {
  return request({
    url: '/archive/waterbox',
    method: 'put',
    data: data
  })
}

// 查询其他设备信息列表
export function listOtherDevice(query) {
  return request({
    url: '/archive/otherDevs/list',
    method: 'get',
    params: query
  })
}

// 新增站点档案-其他设备信息
export function addOtherDevInfo(data) {
  return request({
    url: '/archive/otherDevs',
    method: 'post',
    data: data
  })
}

// 修改站点档案-其他设备信息
export function updateOtherDevInfo(data) {
  return request({
    url: '/archive/otherDevs',
    method: 'put',
    data: data
  })
}

// 导出站点档案信息Excel模板
export function exportArchiveTemplate() {
  return request({
    url: '/archive/template/export',
    method: 'get',
    responseType: 'blob'
  })
}

// 导入站点档案信息
export function importArchiveData(data, productId) {
  return request({
    url: `/archive/import?productId=${productId}`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
