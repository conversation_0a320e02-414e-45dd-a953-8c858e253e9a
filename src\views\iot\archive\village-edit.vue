<template>
    <el-card style="margin: 6px;">
        <!-- 导入导出操作栏 -->
        <!-- <div class="import-export-toolbar" style="margin-bottom: 10px; text-align: right;">
            <el-button
                type="primary"
                icon="el-icon-download"
                size="small"
                @click="exportTemplate">
                导出模板
            </el-button>
            <el-upload
                ref="upload"
                :limit="1"
                accept=".xlsx,.xls"
                :show-file-list="false"
                :on-success="handleImportSuccess"
                :on-error="handleImportError"
                :before-upload="beforeUpload"
                action=""
                :http-request="handleImport"
                style="display: inline-block; margin-left: 10px;">
                <el-button
                    type="success"
                    icon="el-icon-upload2"
                    size="small">
                    导入数据
                </el-button>
            </el-upload>
        </div> -->

        <div class="tree-tabs-container">
            <div style="display: flex;">
            <!-- 左侧树形菜单 -->
            <el-menu
                default-active="1"
                class="el-menu-vertical"
                @select="handleMenuSelect"
            >
                <!-- <div class="villageInfo">
                    <div class="villageTop">
                        <img src="@/assets/404_images/404.png" alt="404">
                        <div>智信小区</div>
                        <div>ID：1123</div>
                        <img src="@/assets/404_images/404.png" alt="404">
                    </div>
                    <div class="villageBottom">
                        地址：佛山市禅城区
                    </div>
                </div> -->
                <el-menu-item index="1" >小区信息</el-menu-item>
                <el-menu-item index="2"
                    :disabled="isCloseMenu"
                    @click.native="handleDisabledClick"
                    >泵房信息</el-menu-item>
                <el-menu-item index="3" :disabled="isCloseMenu" @click.native="handleDisabledClick">水箱信息</el-menu-item>
                <el-menu-item index="4" :disabled="isCloseMenu" @click.native="handleDisabledClick">其他设备信息</el-menu-item>
                <el-menu-item index="5" :disabled="isCloseMenu" @click.native="handleDisabledClick">竣工验收移交登记</el-menu-item>
                <el-menu-item index="6" :disabled="isCloseMenu" @click.native="handleDisabledClick">竣工验收移交设置</el-menu-item>
                <el-menu-item index="7" :disabled="isCloseMenu" @click.native="handleDisabledClick">水池清洗资料台账</el-menu-item>
            </el-menu>

            <!-- 右侧动态 Tab 内容 -->
            <div class="tab-content" style="">
                <div v-if="activeTab === '1'" name="baseInfoCheck">
                    <baseInfoEdit @saveId="handleSaveFromChild" :villageId = "villageId"></baseInfoEdit>
                    <!-- <baseInfo-check ref="baseInfoCheck" :villageId = "villageId" /> -->
                </div>
                <div v-if="activeTab === '2'" name="pumpInfoEdit">
                    <pump-info-edit ref="pumpInfoEdit" :parentFormData="parentData"/>
                </div>
                <div v-if="activeTab === '3'" name="waterboxEdit">
                    <waterbox-edit ref="waterboxEdit" :parentFormData="parentData" />
                </div>
                <div v-if="activeTab === '4'" name="otherDevEdit">
                    <otherDev-edit ref="otherDevEdit" :parentFormData="parentData" />
                </div>
                <div v-if="activeTab === '5'" name="completionAcceptanceRegister">
                    <completion-acceptance-register
                        ref="completionAcceptanceRegister"
                        :parentFormData="parentData"
                        :villageId="villageId"
                        :villageNumber="villageNumber" />
                </div>
                <div v-if="activeTab === '6'" name="completionAcceptanceSetting">
                    <completion-acceptance-setting ref="completionAcceptanceSetting" :parentFormData="parentData" />
                </div>
                <div v-if="activeTab === '7'" name="keepForm">
                    <keep-form ref="keepForm" :restoreFromRoute="false" :villageId="villageId" :hideVillageSelector="true" />
                </div>
            </div>
            </div>
        </div>

    </el-card>
</template>

<script>

import pumpInfoEdit from './pump-info-edit.vue';
import waterboxEdit from './waterbox-edit.vue';
import otherDevEdit from './otherDev-edit.vue';
import baseInfoCheck from './baseInfo-check.vue';
import completionAcceptanceRegister from './completion-acceptance-register.vue';
import completionAcceptanceSetting from './completion-acceptance-setting.vue';
import { exportArchiveTemplate, importArchiveData } from '@/api/archive/pumpInfo';

export default {
    name: 'DeviceEdit',
    dicts: ['iot_device_status', 'iot_location_way', 'iot_process_status'],
    components: {
        baseInfoEdit: () => import('@/views/iot/archive/baseInfo-edit.vue'),
        pumpInfoEdit,
        waterboxEdit,
        otherDevEdit,
        baseInfoCheck,
        completionAcceptanceRegister,
        completionAcceptanceSetting,
        keepForm: () => import('@/views/workOrder/statistics/keepForm.vue')
    },
    watch: {

    },
    computed: {

    },
    data() {
        return {
            activeTab: '1', // 默认激活的菜单项
            villageId:null,
            villageNumber:'',
            isCloseMenu: true,
            // 二维码内容
            qrText: '高明二次供水监控平台',
            //父页面向子页面传输的参数
            parentFormData: {
                villageNumber: ''
            },
            // 表单参数
            form: {
            },
            // 表单校验
            rules: {

            },
            isMediaDevice: false,
            productData: {}
        };
    },
    created() {
        this.parentData = {
            villageNumber: this.$route.query && this.$route.query.villageNumber
        }
        // 获取小区信息
        this.villageId = this.$route.query && this.$route.query.villageId;
        this.villageNumber = this.$route.query && this.$route.query.villageNumber;
        if(this.villageId != null && this.villageId != undefined && this.villageId != 0){
            this.isCloseMenu = false;
        }
    },
    activated() {
        // 跳转选项卡
        let activeName = this.$route.query.activeName;
        if (activeName != null && activeName != '') {
            this.activeName = activeName;
        }
    },
    destroyed() {
    },
    methods: {
        handleMenuSelect(index) {
            this.activeTab = index;
        },
        handleSaveFromChild(data) {
            // 接收子组件传递的数据
            console.log('从子组件接收到的数据：', data);
            if(data.isCloseMenu == false){
                this.isCloseMenu = false;
            }
            this.villageId = data.villageId
            this.villageNumber = data.villageNumber
            this.parentData = {
                villageNumber: data.villageNumber
            }

        },
        handleDisabledClick(menuName) {
            if (this.isCloseMenu) {
                this.$message({
                message: `请先保存小区信息`,
                type: 'warning',
                });
            }
        },

        handleDeviceSaveSuccess() {
            this.$message.success('加压设备信息保存成功');
        },

        // 导出Excel模板
        async exportTemplate() {
            try {
                this.$modal.loading("正在导出模板，请稍候...");
                const response = await exportArchiveTemplate();

                // 创建下载链接
                const blob = new Blob([response], {
                    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                });
                const url = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = '站点档案信息导入模板.xlsx';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                window.URL.revokeObjectURL(url);

                this.$modal.closeLoading();
                this.$modal.msgSuccess("模板导出成功");
            } catch (error) {
                this.$modal.closeLoading();
                this.$modal.msgError("模板导出失败：" + error.message);
            }
        },

        // 上传前验证
        beforeUpload(file) {
            const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                           file.type === 'application/vnd.ms-excel';
            const isLt10M = file.size / 1024 / 1024 < 10;

            if (!isExcel) {
                this.$modal.msgError('只能上传Excel文件!');
                return false;
            }
            if (!isLt10M) {
                this.$modal.msgError('上传文件大小不能超过10MB!');
                return false;
            }
            return true;
        },

        // 自定义上传方法
        async handleImport(options) {
            const formData = new FormData();
            formData.append('file', options.file);

            try {
                this.$modal.loading("正在导入数据，请稍候...");
                const response = await importArchiveData(formData);
                this.$modal.closeLoading();

                if (response.code === 200) {
                    this.$modal.msgSuccess("数据导入成功");
                    // 刷新当前页面数据
                    this.refreshCurrentTab();
                } else {
                    this.$modal.msgError("数据导入失败：" + response.msg);
                }
            } catch (error) {
                this.$modal.closeLoading();
                this.$modal.msgError("数据导入失败：" + error.message);
            }
        },

        // 导入成功回调
        handleImportSuccess(response, file, fileList) {
            this.$modal.msgSuccess("文件上传成功");
        },

        // 导入失败回调
        handleImportError(error, file, fileList) {
            this.$modal.msgError("文件上传失败：" + error.message);
        },

        // 刷新当前标签页数据
        refreshCurrentTab() {
            // 根据当前激活的标签页刷新对应的数据
            if (this.activeTab === '1') {
                // 刷新小区信息
                this.$refs.baseInfoEdit && this.$refs.baseInfoEdit.getInfo();
            } else if (this.activeTab === '2') {
                // 刷新泵房信息
                this.$refs.pumpInfoEdit && this.$refs.pumpInfoEdit.getInfo();
            } else if (this.activeTab === '3') {
                // 刷新水箱信息
                this.$refs.waterboxEdit && this.$refs.waterboxEdit.getInfo();
            }
        },

    },
};
</script>
<style>
.tree-tabs-container{
    height: 100%;
}
.el-menu-vertical {
  width: 150px;
  height: 100vh;
  text-align: end;
}
.el-menu-vertical .is-active{
    color: #1890ff;
    border-right: 2px solid #1890ff;
}
.el-menu-vertical .el-submenu .is-active{
    color: #1890ff;
    border-right: 0px solid #1890ff;
}
/* .el-menu-vertical .el-submenu .is-opened{
    color: #1890ff;
    border-right: none;
} */
.el-menu-vertical .el-menu-item {
    height: 46px;
    line-height: 46px;
}
.el-menu-vertical .el-submenu__title {
    height: 46px;
    line-height: 46px;
}
.el-menu-vertical .el-submenu .el-menu-item {
    height: 36px;
    line-height: 36px;
    padding: 0 45px;
    min-width: 150px;
}


/*菜单关闭*/
.el-menu-vertical .el-submenu>.el-submenu__title .el-submenu__icon-arrow{
	position: absolute;
    top: 50%;
    left: 20px !important;
}
/*菜单展开*/
.el-menu-vertical .el-submenu.is-opened>.el-submenu__title .el-submenu__icon-arrow{
	position: absolute;
    top: 50%;
    left: 20px !important;
}
.el-menu-vertical .el-submenu.is-opened>.el-submenu__title .el-submenu__icon-arrow ::before{
	position: absolute;
    top: 50%;
    left: 20px !important;
}

/* .el-menu-vertical .el-submenu__icon-arrow {
    position: absolute;
    top: 50%;
    left: 20px !important;
}
.el-menu-vertical .el-submenu__icon-arrow .el-icon-arrow-down ::before {
    position: absolute;
    top: 50%;
    left: 20px !important;
} */
/* .el-menu-vertical .el-submenu.is-opened > .el-submenu__title .el-submenu__icon-arrow {
    position: absolute;
    left: 0;
}
.el-menu-vertical .el-submenu.is-closed > .el-submenu__title .el-submenu__icon-arrow {
    position: absolute;
    left: 0;
} */
/* .el-menu-vertical .el-submenu__icon-arrow {
    position: absolute;
    top: 50%;
    left: 20px;
} */
/* .el-menu-vertical .el-icon-arrow-down:before {
    position: absolute;
    top: 50%;
    left: 0;
}
.el-menu-vertical .el-icon-arrow-down:after {
    position: absolute;
    top: 50%;
    left: 0;
} */
.tab-content {
  flex: 1;
  padding: 20px;
}
.villageTop{
    display: flex;
}
.infoTitle{
    font-size: 18px;
    font-weight: bold;
    color: #333;
    border-left: 5px solid #007db6;
    padding-left: 10px;
    line-height: 1.5;
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}
</style>
