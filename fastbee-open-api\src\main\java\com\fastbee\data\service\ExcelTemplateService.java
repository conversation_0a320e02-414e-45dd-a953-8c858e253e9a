package com.fastbee.data.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.fastbee.archive.domain.PumpHouseInfo;
import com.fastbee.archive.domain.WaterTankInfo;
import com.fastbee.archive.service.IPumpHouseInfoService;
import com.fastbee.archive.service.IWaterTankInfoService;
import com.fastbee.common.core.domain.AjaxResult;
import com.fastbee.common.core.domain.entity.SysDictData;
import com.fastbee.common.core.domain.entity.SysVillage;
import com.fastbee.iot.service.ISysVillageService;
import com.fastbee.iot.service.IDeviceService;
import com.fastbee.iot.service.IProductService;
import com.fastbee.iot.domain.Device;
import com.fastbee.iot.domain.Product;
import com.fastbee.data.dto.excel.VillageInfoExcelDto;
import com.fastbee.data.dto.excel.PumpHouseInfoExcelDto;
import com.fastbee.data.dto.excel.WaterTankInfoExcelDto;
import com.fastbee.data.handler.DropDownWriteHandler;
import com.fastbee.system.service.ISysDictTypeService;
import com.fastbee.iot.service.IRegionService;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 导入数据准备类
 */
class ImportDataPreparation {
    private List<Device> devicesToCreate = new ArrayList<>();
    private List<SysVillage> villagesToCreate = new ArrayList<>();
    private List<PumpHouseInfo> pumpHousesToCreate = new ArrayList<>();
    private List<WaterTankInfo> waterTanksToCreate = new ArrayList<>();
    private Map<String, Long> deviceIdMap = new HashMap<>(); // serialNumber -> deviceId

    // getters and setters
    public List<Device> getDevicesToCreate() { return devicesToCreate; }
    public void setDevicesToCreate(List<Device> devicesToCreate) { this.devicesToCreate = devicesToCreate; }

    public List<SysVillage> getVillagesToCreate() { return villagesToCreate; }
    public void setVillagesToCreate(List<SysVillage> villagesToCreate) { this.villagesToCreate = villagesToCreate; }

    public List<PumpHouseInfo> getPumpHousesToCreate() { return pumpHousesToCreate; }
    public void setPumpHousesToCreate(List<PumpHouseInfo> pumpHousesToCreate) { this.pumpHousesToCreate = pumpHousesToCreate; }

    public List<WaterTankInfo> getWaterTanksToCreate() { return waterTanksToCreate; }
    public void setWaterTanksToCreate(List<WaterTankInfo> waterTanksToCreate) { this.waterTanksToCreate = waterTanksToCreate; }

    public Map<String, Long> getDeviceIdMap() { return deviceIdMap; }
    public void setDeviceIdMap(Map<String, Long> deviceIdMap) { this.deviceIdMap = deviceIdMap; }
}

/**
 * Excel模板导出服务
 *
 * <AUTHOR>
 * @date 2025-01-24
 */
@Service
public class ExcelTemplateService {

    @Autowired
    private ISysDictTypeService dictTypeService;

    @Autowired
    private IPumpHouseInfoService pumpHouseInfoService;

    @Autowired
    private IWaterTankInfoService waterTankInfoService;

    @Autowired
    private ISysVillageService sysVillageService;

    @Autowired
    private IRegionService regionService;

    @Autowired
    private IDeviceService deviceService;

    @Autowired
    private IProductService productService;

    /**
     * 导出站点档案信息Excel模板
     */
    public void exportArchiveTemplate(HttpServletResponse response) throws IOException {
        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("站点档案信息导入模板", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        // 获取字典数据
        Map<String, String[]> dictData = getDictData();

        // 创建样式策略和下拉框处理器
        HorizontalCellStyleStrategy horizontalCellStyleStrategy = createCellStyleStrategy();
        DropDownWriteHandler dropDownWriteHandler = new DropDownWriteHandler(dictData);

        try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                .registerWriteHandler(horizontalCellStyleStrategy)
                .registerWriteHandler(dropDownWriteHandler)
                .build()) {

            // 创建小区信息Sheet
            WriteSheet villageSheet = EasyExcel.writerSheet(0, "小区信息")
                    .head(VillageInfoExcelDto.class)
                    .build();

            // 创建泵房信息Sheet
            WriteSheet pumpHouseSheet = EasyExcel.writerSheet(1, "泵房信息")
                    .head(PumpHouseInfoExcelDto.class)
                    .build();

            // 创建水箱信息Sheet
            WriteSheet waterTankSheet = EasyExcel.writerSheet(2, "水箱信息")
                    .head(WaterTankInfoExcelDto.class)
                    .build();

            // 写入示例数据
            List<VillageInfoExcelDto> villageData = createVillageSampleData();
            List<PumpHouseInfoExcelDto> pumpHouseData = createPumpHouseSampleData();
            List<WaterTankInfoExcelDto> waterTankData = createWaterTankSampleData();

            excelWriter.write(villageData, villageSheet);
            excelWriter.write(pumpHouseData, pumpHouseSheet);
            excelWriter.write(waterTankData, waterTankSheet);
        }
    }

    /**
     * 创建单元格样式策略
     */
    private HorizontalCellStyleStrategy createCellStyleStrategy() {
        // 头的策略
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        headWriteCellStyle.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontHeightInPoints((short) 12);
        headWriteFont.setBold(true);
        headWriteCellStyle.setWriteFont(headWriteFont);
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        headWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 内容的策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        return new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
    }







    /**
     * 获取字典数据
     */
    private Map<String, String[]> getDictData() {
        Map<String, String[]> dictData = new HashMap<>();

        // 1. 泵房类型：archive_pump_type
        List<SysDictData> pumpTypeList = dictTypeService.selectDictDataByType("archive_pump_type");
        if (pumpTypeList != null) {
            String[] pumpTypeArray = pumpTypeList.stream()
                    .map(SysDictData::getDictLabel)
                    .toArray(String[]::new);
            dictData.put("pumpType", pumpTypeArray);
        }

        // 2. 建筑类别：archive_building_type
        List<SysDictData> buildingTypeList = dictTypeService.selectDictDataByType("archive_building_type");
        if (buildingTypeList != null) {
            String[] buildingTypeArray = buildingTypeList.stream()
                    .map(SysDictData::getDictLabel)
                    .toArray(String[]::new);
            dictData.put("buildingType", buildingTypeArray);
        }

        // 3. 泵房级别：archive_house_level
        List<SysDictData> houseLevelList = dictTypeService.selectDictDataByType("archive_house_level");
        if (houseLevelList != null) {
            String[] houseLevelArray = houseLevelList.stream()
                    .map(SysDictData::getDictLabel)
                    .toArray(String[]::new);
            dictData.put("houseLevel", houseLevelArray);
        }

        // 4. 接管情况：archive_control_status
        List<SysDictData> controlStatusList = dictTypeService.selectDictDataByType("archive_control_status");
        if (controlStatusList != null) {
            String[] controlStatusArray = controlStatusList.stream()
                    .map(SysDictData::getDictLabel)
                    .toArray(String[]::new);
            dictData.put("controlStatus", controlStatusArray);
        }

        // 5. 泵房建设状态：archive_build_status
        List<SysDictData> buildStatusList = dictTypeService.selectDictDataByType("archive_build_status");
        if (buildStatusList != null) {
            String[] buildStatusArray = buildStatusList.stream()
                    .map(SysDictData::getDictLabel)
                    .toArray(String[]::new);
            dictData.put("buildStatus", buildStatusArray);
        }

        // 6. 水箱材质：archive_tank_material
        List<SysDictData> tankMaterialList = dictTypeService.selectDictDataByType("archive_tank_material");
        if (tankMaterialList != null) {
            String[] tankMaterialArray = tankMaterialList.stream()
                    .map(SysDictData::getDictLabel)
                    .toArray(String[]::new);
            dictData.put("tankMaterial", tankMaterialArray);
        }

        // 7. 进水阀类型：archive_tank_inlet_valve
        List<SysDictData> inletValveList = dictTypeService.selectDictDataByType("archive_tank_inlet_valve");
        if (inletValveList != null) {
            String[] inletValveArray = inletValveList.stream()
                    .map(SysDictData::getDictLabel)
                    .toArray(String[]::new);
            dictData.put("inletValve", inletValveArray);
        }

        // 8. 监听液位器：archive_tank_monitor_liquid
        List<SysDictData> monitorLiquidList = dictTypeService.selectDictDataByType("archive_tank_monitor_liquid");
        if (monitorLiquidList != null) {
            String[] monitorLiquidArray = monitorLiquidList.stream()
                    .map(SysDictData::getDictLabel)
                    .toArray(String[]::new);
            dictData.put("monitorLiquid", monitorLiquidArray);
        }

        // 获取是否选项字典
        String[] yesNoOptions = {"是", "否"};
        dictData.put("yesNo", yesNoOptions);

        // 9. 区域数据：获取叶子节点区域
        List<Map<String, Object>> regionList = regionService.getLeafRegions();
        if (regionList != null && !regionList.isEmpty()) {
            String[] regionArray = regionList.stream()
                    .map(region -> region.get("label").toString())
                    .toArray(String[]::new);
            dictData.put("regions", regionArray);
        }

        return dictData;
    }

    /**
     * 创建小区信息示例数据
     */
    private List<VillageInfoExcelDto> createVillageSampleData() {
        List<VillageInfoExcelDto> data = new ArrayList<>();
        VillageInfoExcelDto sample = new VillageInfoExcelDto();
        sample.setVillageNumber("示例：VN001");
        sample.setVillageName("示例：智信小区");
        sample.setDetailedAddress("示例：广东省佛山市高明区荷城街道");
        sample.setRegionId("示例：荷城街道");  // 对应区域叶子节点
//        sample.setLat("示例：22.8833");
//        sample.setLng("示例：112.8833");
        sample.setTotalHouses("示例：500");
        sample.setDirectHouses("示例：200");
        sample.setPressurizedHouses("示例：300");
        sample.setBuildings("示例：10");
        sample.setCategoryId("示例：住宅");  // 对应字典：archive_building_type
        sample.setYearBuilt("示例：2020-01-01");
        sample.setSituationBuilt("示例：已完工");
        sample.setDeveloper("示例：某某房地产开发有限公司");
        sample.setDeveloperPhone("示例：0757-88888888");
        sample.setTenement("示例：某某物业管理有限公司");
        sample.setTenementPhone("示例：0757-99999999");
        sample.setGridPhone("示例：13800138000");
        sample.setRemark("示例：备注信息");
        data.add(sample);
        return data;
    }

    /**
     * 创建泵房信息示例数据
     */
    private List<PumpHouseInfoExcelDto> createPumpHouseSampleData() {
        List<PumpHouseInfoExcelDto> data = new ArrayList<>();
        PumpHouseInfoExcelDto sample = new PumpHouseInfoExcelDto();
//        sample.setVillageNumber("示例：VN001");
        sample.setHouseType("示例：地下泵房");  // 对应字典：archive_pump_type
        sample.setHouseLevel("示例：一级");     // 对应字典：archive_house_level
        sample.setHouseLocation("示例：地下一层");
        sample.setBuildStatus("示例：已完工");  // 对应字典：archive_build_status
        sample.setGroupId("示例：1");
        sample.setBuildingType("示例：住宅");   // 对应字典：archive_building_type
        sample.setControlStatus("示例：已接管"); // 对应字典：archive_control_status
        sample.setControlDate("示例：2024-01-01");
        sample.setSupplyMode("示例：变频供水");
        sample.setSupplyScale("示例：50");
        sample.setTankNumber("示例：2");
        sample.setSupplyBuildings("示例：5");
        sample.setPressureNumber("示例：100");
        sample.setDirectFloors("示例：1-3层");
        sample.setPressureFloors("示例：4-15层");
        sample.setIsShared("示例：是");        // 对应选项：是/否
        data.add(sample);
        return data;
    }

    /**
     * 创建水箱信息示例数据
     */
    private List<WaterTankInfoExcelDto> createWaterTankSampleData() {
        List<WaterTankInfoExcelDto> data = new ArrayList<>();
        WaterTankInfoExcelDto sample = new WaterTankInfoExcelDto();
//        sample.setVillageNumber("示例：VN001");
        sample.setLocation("示例：楼顶");
        sample.setMaterial("示例：不锈钢");      // 对应字典：archive_tank_material
        sample.setIsRule("示例：是");           // 对应选项：是/否
        sample.setLengthNum("示例：5.0");
        sample.setWideNum("示例：3.0");
        sample.setHighNum("示例：2.0");
        sample.setVolumeNum("示例：30.0");
        sample.setCleanCycle("示例：180");
        sample.setInletValve("示例：浮球阀");    // 对应字典：archive_tank_inlet_valve
        sample.setMonitorLiquidLevel("示例：有"); // 对应字典：archive_tank_monitor_liquid
        sample.setIsShared("示例：否");         // 对应选项：是/否
        sample.setHasDrainageOverflow("示例：是"); // 对应选项：是/否
        sample.setHasBreather("示例：是");      // 对应选项：是/否
        sample.setHeightAboveGround("示例：1.5");
        sample.setPipeHeight("示例：0.3");
        sample.setPipeDiameter("示例：100");
        sample.setTankDescribe("示例：标准水箱");
        sample.setIsGrid("示例：是");           // 对应选项：是/否
        sample.setRemark("示例：定期清洗维护");
        data.add(sample);
        return data;
    }

    /**
     * 导入站点档案信息
     */
    public AjaxResult importArchiveData(MultipartFile file, Long productId) {
        try {
            if (file.isEmpty()) {
                return AjaxResult.error("上传文件不能为空");
            }

            // 验证产品ID
            if (productId == null) {
                return AjaxResult.error("产品ID不能为空");
            }

            // 验证产品是否存在
            Product product = productService.selectProductByProductId(productId);
            if (product == null) {
                return AjaxResult.error("产品不存在");
            }

            // 验证文件格式
            String fileName = file.getOriginalFilename();
            if (fileName == null || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
                return AjaxResult.error("请上传Excel格式文件");
            }

            // 读取Excel文件的各个Sheet
            List<VillageInfoExcelDto> villageList = EasyExcel.read(file.getInputStream())
                    .head(VillageInfoExcelDto.class)
                    .sheet("小区信息")
                    .doReadSync();

            List<PumpHouseInfoExcelDto> pumpHouseList = EasyExcel.read(file.getInputStream())
                    .head(PumpHouseInfoExcelDto.class)
                    .sheet("泵房信息")
                    .doReadSync();

            List<WaterTankInfoExcelDto> waterTankList = EasyExcel.read(file.getInputStream())
                    .head(WaterTankInfoExcelDto.class)
                    .sheet("水箱信息")
                    .doReadSync();

            // 第一阶段：数据预检查和准备
            ImportDataPreparation preparation = new ImportDataPreparation();
            StringBuilder errorMsg = new StringBuilder();

            // 预检查所有数据
            AjaxResult validationResult = validateAndPrepareData(villageList, pumpHouseList, waterTankList,
                                                               product, preparation, errorMsg);
            if (validationResult != null) {
                return validationResult; // 验证失败，直接返回错误
            }

            // 第二阶段：批量执行数据库操作
            return executeImportOperations(preparation);

        } catch (Exception e) {
            return AjaxResult.error("导入失败：" + e.getMessage());
        }
    }

    /**
     * 第一阶段：验证和准备所有数据
     */
    private AjaxResult validateAndPrepareData(List<VillageInfoExcelDto> villageList,
                                            List<PumpHouseInfoExcelDto> pumpHouseList,
                                            List<WaterTankInfoExcelDto> waterTankList,
                                            Product product,
                                            ImportDataPreparation preparation,
                                            StringBuilder errorMsg) {

        // 验证和准备小区信息
        if (villageList != null && !villageList.isEmpty()) {
            for (int i = 0; i < villageList.size(); i++) {
                VillageInfoExcelDto dto = villageList.get(i);
                if (!isValidVillageData(dto)) {
                    return AjaxResult.error("小区信息第" + (i + 2) + "行数据不完整");
                }

                String serialNumber = dto.getVillageNumber();
                Device existingDevice = deviceService.selectDeviceBySerialNumber(serialNumber);
                Long deviceId;

                if (existingDevice == null) {
                    // 设备不存在，准备创建新设备
                    Device newDevice = new Device();
                    newDevice.setSerialNumber(serialNumber);
                    newDevice.setDeviceName(dto.getVillageName());
                    newDevice.setProductId(product.getProductId());
                    newDevice.setProductName(product.getProductName());
                    newDevice.setLocationWay(product.getLocationWay());
                    newDevice.setStatus(1); // 1-未激活
                    newDevice.setIsShadow(0);
                    newDevice.setRssi(0);

                    preparation.getDevicesToCreate().add(newDevice);
                    // 暂时使用负数作为占位符，后续会替换为实际ID
                    deviceId = -(long)(i + 1);
                } else {
                    // 设备存在，直接使用现有设备ID
                    deviceId = existingDevice.getDeviceId();
                }

                preparation.getDeviceIdMap().put(serialNumber, deviceId);

                // 准备小区信息
                SysVillage entity = convertToSysVillage(dto);
                entity.setSerialNumber(serialNumber);
                entity.setDeviceId(deviceId.toString());
                preparation.getVillagesToCreate().add(entity);
            }
        }

        // 验证和准备泵房信息
        if (pumpHouseList != null && !pumpHouseList.isEmpty()) {
            for (int i = 0; i < pumpHouseList.size(); i++) {
                PumpHouseInfoExcelDto dto = pumpHouseList.get(i);
                if (!isValidPumpHouseData(dto)) {
                    return AjaxResult.error("泵房信息第" + (i + 2) + "行数据不完整");
                }

                PumpHouseInfo entity = convertToPumpHouseInfo(dto);
                preparation.getPumpHousesToCreate().add(entity);
            }
        }

        // 验证和准备水箱信息
        if (waterTankList != null && !waterTankList.isEmpty()) {
            for (int i = 0; i < waterTankList.size(); i++) {
                WaterTankInfoExcelDto dto = waterTankList.get(i);
                if (!isValidWaterTankData(dto)) {
                    return AjaxResult.error("水箱信息第" + (i + 2) + "行数据不完整");
                }

                WaterTankInfo entity = convertToWaterTankInfo(dto);
                preparation.getWaterTanksToCreate().add(entity);
            }
        }

        return null; // 验证通过
    }

    /**
     * 第二阶段：执行数据库操作
     */
    private AjaxResult executeImportOperations(ImportDataPreparation preparation) {
        int successCount = 0;

        try {
            // 1. 创建新设备
            for (Device device : preparation.getDevicesToCreate()) {
                Device createdDevice = deviceService.insertDevice(device);
                // 更新设备ID映射
                preparation.getDeviceIdMap().put(device.getSerialNumber(), createdDevice.getDeviceId());
                successCount++;
            }

            // 2. 更新小区信息中的设备ID
            for (SysVillage village : preparation.getVillagesToCreate()) {
                Long realDeviceId = preparation.getDeviceIdMap().get(village.getSerialNumber());
                village.setDeviceId(realDeviceId.toString());
                sysVillageService.insertSysVillage(village);
                successCount++;
            }

            // 3. 创建泵房信息
            for (PumpHouseInfo pumpHouse : preparation.getPumpHousesToCreate()) {
                pumpHouseInfoService.insertPumpHouseInfo(pumpHouse);
                successCount++;
            }

            // 4. 创建水箱信息
            for (WaterTankInfo waterTank : preparation.getWaterTanksToCreate()) {
                waterTankInfoService.insertWaterTankInfo(waterTank);
                successCount++;
            }

            return AjaxResult.success("导入成功，共导入" + successCount + "条数据");

        } catch (Exception e) {
            // 如果在执行阶段出现错误，需要手动清理已创建的数据
            rollbackCreatedData(preparation);
            return AjaxResult.error("导入失败：" + e.getMessage() + "，已清理部分数据");
        }
    }

    /**
     * 手动回滚已创建的数据
     */
    private void rollbackCreatedData(ImportDataPreparation preparation) {
        try {
            // 删除已创建的设备（这会级联删除相关的小区信息）
            for (Device device : preparation.getDevicesToCreate()) {
                if (device.getDeviceId() != null) {
                    deviceService.deleteDeviceByDeviceId(device.getDeviceId());
                }
            }
        } catch (Exception e) {
            // 记录回滚失败的日志，但不抛出异常
            System.err.println("回滚数据时发生错误：" + e.getMessage());
        }
    }

    /**
     * 验证小区信息数据
     */
    private boolean isValidVillageData(VillageInfoExcelDto dto) {
        return dto != null &&
               dto.getVillageNumber() != null &&
               !dto.getVillageNumber().trim().isEmpty() &&
               dto.getVillageName() != null &&
               !dto.getVillageName().trim().isEmpty();
    }

    /**
     * 验证泵房信息数据
     */
    private boolean isValidPumpHouseData(PumpHouseInfoExcelDto dto) {
        return dto != null;
    }

    /**
     * 验证水箱信息数据
     */
    private boolean isValidWaterTankData(WaterTankInfoExcelDto dto) {
        return dto != null ;
    }



    /**
     * 转换小区信息DTO为实体
     */
    private SysVillage convertToSysVillage(VillageInfoExcelDto dto) {
        SysVillage entity = new SysVillage();
        BeanUtils.copyProperties(dto, entity);

        // 处理区域ID：根据区域名称查询区域ID
        if (dto.getRegionId() != null && !dto.getRegionId().trim().isEmpty()) {
            String regionName = dto.getRegionId().trim();
            // 移除示例前缀
            if (regionName.startsWith("示例：")) {
                regionName = regionName.substring(3);
            }

            try {
                // 调用区域服务根据名称查询区域ID
                Long regionId = regionService.getRegionIdByName(regionName);
                if (regionId != null) {
                    entity.setRegionId(regionId.toString());
                } else {
                    // 如果找不到对应的区域，记录警告但不阻止导入
                    System.out.println("警告：找不到区域名称为 '" + regionName + "' 的区域");
                    entity.setRegionId(null);
                }
            } catch (Exception e) {
                System.out.println("查询区域ID时发生错误：" + e.getMessage());
                entity.setRegionId(null);
            }
        }

        if (dto.getTotalHouses() != null && !dto.getTotalHouses().trim().isEmpty()) {
            try {
                entity.setTotalHouses(Long.parseLong(dto.getTotalHouses()));
            } catch (NumberFormatException e) {
                // 忽略转换错误
            }
        }

        if (dto.getDirectHouses() != null && !dto.getDirectHouses().trim().isEmpty()) {
            try {
                entity.setDirectHouses(Long.parseLong(dto.getDirectHouses()));
            } catch (NumberFormatException e) {
                // 忽略转换错误
            }
        }

        if (dto.getPressurizedHouses() != null && !dto.getPressurizedHouses().trim().isEmpty()) {
            try {
                entity.setPressurizedHouses(Long.parseLong(dto.getPressurizedHouses()));
            } catch (NumberFormatException e) {
                // 忽略转换错误
            }
        }

        if (dto.getBuildings() != null && !dto.getBuildings().trim().isEmpty()) {
            try {
                entity.setBuildings(Long.parseLong(dto.getBuildings()));
            } catch (NumberFormatException e) {
                // 忽略转换错误
            }
        }

        return entity;
    }

    /**
     * 转换泵房信息DTO为实体
     */
    private PumpHouseInfo convertToPumpHouseInfo(PumpHouseInfoExcelDto dto) {
        PumpHouseInfo entity = new PumpHouseInfo();
        BeanUtils.copyProperties(dto, entity);

        // 处理是否字段转换
        if ("是".equals(dto.getIsShared())) {
            entity.setIsShared(1);
        } else if ("否".equals(dto.getIsShared())) {
            entity.setIsShared(0);
        }

        return entity;
    }

    /**
     * 转换水箱信息DTO为实体
     */
    private WaterTankInfo convertToWaterTankInfo(WaterTankInfoExcelDto dto) {
        WaterTankInfo entity = new WaterTankInfo();
        BeanUtils.copyProperties(dto, entity);

        // 处理是否字段转换
        if ("是".equals(dto.getIsRule())) {
            entity.setIsRule(1);
        } else if ("否".equals(dto.getIsRule())) {
            entity.setIsRule(0);
        }

        if ("是".equals(dto.getIsShared())) {
            entity.setIsShared(1);
        } else if ("否".equals(dto.getIsShared())) {
            entity.setIsShared(0);
        }

        if ("是".equals(dto.getHasDrainageOverflow())) {
            entity.setHasDrainageOverflow(1);
        } else if ("否".equals(dto.getHasDrainageOverflow())) {
            entity.setHasDrainageOverflow(0);
        }

        if ("是".equals(dto.getHasBreather())) {
            entity.setHasBreather(1);
        } else if ("否".equals(dto.getHasBreather())) {
            entity.setHasBreather(0);
        }

        if ("是".equals(dto.getIsGrid())) {
            entity.setIsGrid(1);
        } else if ("否".equals(dto.getIsGrid())) {
            entity.setIsGrid(0);
        }

        return entity;
    }


}
