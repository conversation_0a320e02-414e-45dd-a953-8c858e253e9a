package com.fastbee.data.dto.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;

/**
 * 水箱信息Excel导入导出DTO
 *
 * <AUTHOR>
 * @date 2025-01-24
 */
@ContentRowHeight(20)
@HeadRowHeight(25)
public class WaterTankInfoExcelDto {

//    @ExcelProperty(value = "小区编号*", index = 0)
//    @ColumnWidth(20)
    private String villageNumber;

    public String getVillageNumber() {
        return villageNumber;
    }

    public void setVillageNumber(String villageNumber) {
        this.villageNumber = villageNumber;
    }

    @ExcelProperty(value = "位置", index = 0)
    @ColumnWidth(20)
    private String location;

    @ExcelProperty(value = "材质", index = 1)
    @ColumnWidth(15)
    private String material;

    @ExcelProperty(value = "是否规则", index = 2)
    @ColumnWidth(15)
    private String isRule;

    @ExcelProperty(value = "长(m)", index = 3)
    @ColumnWidth(15)
    private String lengthNum;

    @ExcelProperty(value = "宽(m)", index = 4)
    @ColumnWidth(15)
    private String wideNum;

    @ExcelProperty(value = "高(m)", index = 5)
    @ColumnWidth(15)
    private String highNum;

    @ExcelProperty(value = "容积(m³)", index = 6)
    @ColumnWidth(15)
    private String volumeNum;

    @ExcelProperty(value = "清洗周期(天)", index = 7)
    @ColumnWidth(20)
    private String cleanCycle;

    @ExcelProperty(value = "进水阀类型", index = 8)
    @ColumnWidth(15)
    private String inletValve;

    @ExcelProperty(value = "监听液位器", index = 9)
    @ColumnWidth(15)
    private String monitorLiquidLevel;

    @ExcelProperty(value = "是否生消共用", index = 10)
    @ColumnWidth(15)
    private String isShared;

    @ExcelProperty(value = "排污溢流装置", index = 11)
    @ColumnWidth(20)
    private String hasDrainageOverflow;

    @ExcelProperty(value = "通气装置", index = 12)
    @ColumnWidth(15)
    private String hasBreather;

    @ExcelProperty(value = "水箱离地高度(m)", index = 13)
    @ColumnWidth(20)
    private String heightAboveGround;

    @ExcelProperty(value = "溢流管高(m)", index = 14)
    @ColumnWidth(15)
    private String pipeHeight;

    @ExcelProperty(value = "进水口管径(mm)", index = 15)
    @ColumnWidth(20)
    private String pipeDiameter;

    @ExcelProperty(value = "水箱设施", index = 16)
    @ColumnWidth(20)
    private String tankDescribe;

    @ExcelProperty(value = "分格情况", index = 17)
    @ColumnWidth(15)
    private String isGrid;

    @ExcelProperty(value = "备注", index = 18)
    @ColumnWidth(30)
    private String remark;

    // Getters and Setters
//    public String getVillageNumber() {
//        return villageNumber;
//    }
//
//    public void setVillageNumber(String villageNumber) {
//        this.villageNumber = villageNumber;
//    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getMaterial() {
        return material;
    }

    public void setMaterial(String material) {
        this.material = material;
    }

    public String getIsRule() {
        return isRule;
    }

    public void setIsRule(String isRule) {
        this.isRule = isRule;
    }

    public String getLengthNum() {
        return lengthNum;
    }

    public void setLengthNum(String lengthNum) {
        this.lengthNum = lengthNum;
    }

    public String getWideNum() {
        return wideNum;
    }

    public void setWideNum(String wideNum) {
        this.wideNum = wideNum;
    }

    public String getHighNum() {
        return highNum;
    }

    public void setHighNum(String highNum) {
        this.highNum = highNum;
    }

    public String getVolumeNum() {
        return volumeNum;
    }

    public void setVolumeNum(String volumeNum) {
        this.volumeNum = volumeNum;
    }

    public String getCleanCycle() {
        return cleanCycle;
    }

    public void setCleanCycle(String cleanCycle) {
        this.cleanCycle = cleanCycle;
    }

    public String getInletValve() {
        return inletValve;
    }

    public void setInletValve(String inletValve) {
        this.inletValve = inletValve;
    }

    public String getMonitorLiquidLevel() {
        return monitorLiquidLevel;
    }

    public void setMonitorLiquidLevel(String monitorLiquidLevel) {
        this.monitorLiquidLevel = monitorLiquidLevel;
    }

    public String getIsShared() {
        return isShared;
    }

    public void setIsShared(String isShared) {
        this.isShared = isShared;
    }

    public String getHasDrainageOverflow() {
        return hasDrainageOverflow;
    }

    public void setHasDrainageOverflow(String hasDrainageOverflow) {
        this.hasDrainageOverflow = hasDrainageOverflow;
    }

    public String getHasBreather() {
        return hasBreather;
    }

    public void setHasBreather(String hasBreather) {
        this.hasBreather = hasBreather;
    }

    public String getHeightAboveGround() {
        return heightAboveGround;
    }

    public void setHeightAboveGround(String heightAboveGround) {
        this.heightAboveGround = heightAboveGround;
    }

    public String getPipeHeight() {
        return pipeHeight;
    }

    public void setPipeHeight(String pipeHeight) {
        this.pipeHeight = pipeHeight;
    }

    public String getPipeDiameter() {
        return pipeDiameter;
    }

    public void setPipeDiameter(String pipeDiameter) {
        this.pipeDiameter = pipeDiameter;
    }

    public String getTankDescribe() {
        return tankDescribe;
    }

    public void setTankDescribe(String tankDescribe) {
        this.tankDescribe = tankDescribe;
    }

    public String getIsGrid() {
        return isGrid;
    }

    public void setIsGrid(String isGrid) {
        this.isGrid = isGrid;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
