package com.fastbee.data.dto.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;

/**
 * 泵房信息Excel导入导出DTO
 *
 * <AUTHOR>
 * @date 2025-01-24
 */
@ContentRowHeight(20)
@HeadRowHeight(25)
public class PumpHouseInfoExcelDto {

    public String getVillageNumber() {
        return villageNumber;
    }

    public void setVillageNumber(String villageNumber) {
        this.villageNumber = villageNumber;
    }

    //    @ExcelProperty(value = "小区编号*", index = 0)
//    @ColumnWidth(20)
    private String villageNumber;

    @ExcelProperty(value = "泵房类型", index = 0)
    @ColumnWidth(15)
    private String houseType;

    @ExcelProperty(value = "泵房级别", index = 1)
    @ColumnWidth(15)
    private String houseLevel;

    @ExcelProperty(value = "泵房位置", index = 2)
    @ColumnWidth(20)
    private String houseLocation;

    @ExcelProperty(value = "建设状态", index = 3)
    @ColumnWidth(15)
    private String buildStatus;

    @ExcelProperty(value = "所属组织ID", index = 4)
    @ColumnWidth(15)
    private String groupId;

    @ExcelProperty(value = "建筑类别", index = 5)
    @ColumnWidth(15)
    private String buildingType;

    @ExcelProperty(value = "接管情况", index = 6)
    @ColumnWidth(15)
    private String controlStatus;

    @ExcelProperty(value = "接管时间", index = 7)
    @ColumnWidth(20)
    private String controlDate;

    @ExcelProperty(value = "供水模式", index = 8)
    @ColumnWidth(15)
    private String supplyMode;

    @ExcelProperty(value = "供水规模(m³/h)", index = 9)
    @ColumnWidth(20)
    private String supplyScale;

    @ExcelProperty(value = "水箱数量", index = 10)
    @ColumnWidth(15)
    private String tankNumber;

    @ExcelProperty(value = "供水楼栋数", index = 11)
    @ColumnWidth(15)
    private String supplyBuildings;

    @ExcelProperty(value = "加压总户数", index = 12)
    @ColumnWidth(15)
    private String pressureNumber;

    @ExcelProperty(value = "直供楼层", index = 13)
    @ColumnWidth(15)
    private String directFloors;

    @ExcelProperty(value = "加压楼层", index = 14)
    @ColumnWidth(15)
    private String pressureFloors;

    @ExcelProperty(value = "是否生消共用", index = 15)
    @ColumnWidth(15)
    private String isShared;

    // Getters and Setters
//    public String getVillageNumber() {
//        return villageNumber;
//    }
//
//    public void setVillageNumber(String villageNumber) {
//        this.villageNumber = villageNumber;
//    }

    public String getHouseType() {
        return houseType;
    }

    public void setHouseType(String houseType) {
        this.houseType = houseType;
    }

    public String getHouseLevel() {
        return houseLevel;
    }

    public void setHouseLevel(String houseLevel) {
        this.houseLevel = houseLevel;
    }

    public String getHouseLocation() {
        return houseLocation;
    }

    public void setHouseLocation(String houseLocation) {
        this.houseLocation = houseLocation;
    }

    public String getBuildStatus() {
        return buildStatus;
    }

    public void setBuildStatus(String buildStatus) {
        this.buildStatus = buildStatus;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getBuildingType() {
        return buildingType;
    }

    public void setBuildingType(String buildingType) {
        this.buildingType = buildingType;
    }

    public String getControlStatus() {
        return controlStatus;
    }

    public void setControlStatus(String controlStatus) {
        this.controlStatus = controlStatus;
    }

    public String getControlDate() {
        return controlDate;
    }

    public void setControlDate(String controlDate) {
        this.controlDate = controlDate;
    }

    public String getSupplyMode() {
        return supplyMode;
    }

    public void setSupplyMode(String supplyMode) {
        this.supplyMode = supplyMode;
    }

    public String getSupplyScale() {
        return supplyScale;
    }

    public void setSupplyScale(String supplyScale) {
        this.supplyScale = supplyScale;
    }

    public String getTankNumber() {
        return tankNumber;
    }

    public void setTankNumber(String tankNumber) {
        this.tankNumber = tankNumber;
    }

    public String getSupplyBuildings() {
        return supplyBuildings;
    }

    public void setSupplyBuildings(String supplyBuildings) {
        this.supplyBuildings = supplyBuildings;
    }

    public String getPressureNumber() {
        return pressureNumber;
    }

    public void setPressureNumber(String pressureNumber) {
        this.pressureNumber = pressureNumber;
    }

    public String getDirectFloors() {
        return directFloors;
    }

    public void setDirectFloors(String directFloors) {
        this.directFloors = directFloors;
    }

    public String getPressureFloors() {
        return pressureFloors;
    }

    public void setPressureFloors(String pressureFloors) {
        this.pressureFloors = pressureFloors;
    }

    public String getIsShared() {
        return isShared;
    }

    public void setIsShared(String isShared) {
        this.isShared = isShared;
    }
}
