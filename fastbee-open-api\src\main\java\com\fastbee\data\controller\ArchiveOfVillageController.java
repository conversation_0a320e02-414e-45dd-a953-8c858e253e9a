package com.fastbee.data.controller;

import com.alibaba.fastjson.JSONObject;
import com.fastbee.archive.domain.*;
import com.alibaba.fastjson2.JSON;
import com.fastbee.archive.service.*;
import com.fastbee.common.core.controller.BaseController;
import com.fastbee.common.core.domain.AjaxResult;
import com.fastbee.common.core.page.TableDataInfo;
import com.fastbee.data.service.ExcelTemplateService;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 站点档案Controller
 * 
 * <AUTHOR>
 * @date 2025-04-16
 */
@Api(tags = "站点档案")
@RestController
@RequestMapping("/archive")
public class ArchiveOfVillageController extends BaseController
{
    @Autowired
    private IPumpHouseInfoService pumpHouseInfoService;

    @Autowired
    private IPumpHouseManageService pumpHouseManageService;

    @Autowired
    private IPressureDevicesInfoService pressureDevicesInfoService;

    @Autowired
    private IWaterTankInfoService waterTankInfoService;

    @Autowired
    private IOtherDevicesService otherDevicesService;

    @Autowired
    private ExcelTemplateService excelTemplateService;

    /**
     * 获取站点档案-泵房基本信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('archive:pumpInfo:query')")
    @GetMapping(value = "/pumpInfo/{id}")
    @ApiOperation("获取泵房基本信息详情")
    public AjaxResult getPumpInfo(@PathVariable("id") Long id)
    {
        System.out.println(" =+++++++++++++++++++++++++++++++++ "+id);
        return success(pumpHouseInfoService.selectPumpHouseInfoById(id));
    }

    /**
     * 获取站点档案-泵房基本信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('archive:pumpInfo:query')")
    @GetMapping(value = "/pumpInfo/v2/{villageNumber}")
    @ApiOperation("通过站点编号获取泵房信息详情")
    public AjaxResult getPumpInfoByVillageNumber(@PathVariable("villageNumber") String villageNumber)
    {
        System.out.println(" =+++++++++++++++++++++++++++++++++ "+villageNumber);
        PumpHouseInfo pumpHouseInfo = pumpHouseInfoService.selectPumpHouseInfoByVillageNumber(villageNumber);
        PumpHouseManage pumpHouseManage = pumpHouseManageService.selectPumpHouseManageByVillageNumber(villageNumber);
        Map<String, Object> valMap = Maps.newHashMap();
        valMap.put("pumpHouseInfo", pumpHouseInfo);
        valMap.put("pumpHouseManage", pumpHouseManage);
        System.out.println("valMap = " + valMap);
        return success(valMap);
    }

    /**
     * 新增站点档案-泵房信息（基本信息和管理信息）
     */
    @PreAuthorize("@ss.hasPermi('archive:pumpInfo:add')")
    @PostMapping(value = "/pumpInfo")
    @ApiOperation("添加泵房信息")
    public AjaxResult addPumpInfo(@RequestBody  Map<String, String> pumpHouseMap)
    {
        System.out.println("pumpHouseMappumpHouseMappumpHouseMap = " + pumpHouseMap);
        PumpHouseInfo pumpHouseInfo = JSON.parseObject(pumpHouseMap.get("pumpInfo"), PumpHouseInfo.class);
        pumpHouseInfoService.deletePumpHouseInfoByVn(pumpHouseInfo.getVillageNumber());
        pumpHouseManageService.deletePumpHouseManageByVn(pumpHouseInfo.getVillageNumber());

        //新增泵房基本信息
        AjaxResult infoResult = AjaxResult.success();
        if (!(pumpHouseMap.get("pumpInfo").equals("{}"))) {
            infoResult = toAjax(pumpHouseInfoService.insertPumpHouseInfo(pumpHouseInfo));
        }

        //新增泵房管理信息
        AjaxResult manageResult = AjaxResult.success();
        if (!(pumpHouseMap.get("manageInfo").equals("{}"))) {
            PumpHouseManage pumpHouseManage = JSON.parseObject(pumpHouseMap.get("manageInfo"), PumpHouseManage.class);
            manageResult = toAjax(pumpHouseManageService.insertPumpHouseManage(pumpHouseManage));
        }

        if (infoResult.get("code").equals(200) && manageResult.get("code").equals(200)) {
            return AjaxResult.success();
        } else {
            return AjaxResult.error();
        }
    }

    /**
     * 修改站点档案-泵房信息（基本信息和管理信息）
     */
    @PreAuthorize("@ss.hasPermi('archive:pumpInfo:edit')")
    @PutMapping(value = "/pumpInfo")
    @ApiOperation("修改泵房信息")
    public AjaxResult edit(@RequestBody  Map<String, String> pumpHouseMap)
    {
        PumpHouseInfo pumpHouseInfo = JSON.parseObject(pumpHouseMap.get("pumpInfo"), PumpHouseInfo.class);

        pumpHouseInfoService.deletePumpHouseInfoByVn(pumpHouseInfo.getVillageNumber());
        pumpHouseManageService.deletePumpHouseManageByVn(pumpHouseInfo.getVillageNumber());

        //修改泵房基本信息
        AjaxResult infoResult = toAjax(pumpHouseInfoService.insertPumpHouseInfo(pumpHouseInfo));

        //修改泵房管理信息
        PumpHouseManage pumpHouseManage = JSON.parseObject(pumpHouseMap.get("manageInfo"), PumpHouseManage.class);
        AjaxResult manageResult = toAjax(pumpHouseManageService.insertPumpHouseManage(pumpHouseManage));

        if (infoResult.get("code").equals(200) && manageResult.get("code").equals(200)) {
            return AjaxResult.success();
        } else {
            return AjaxResult.error();
        }

    }

    /**
     * 获取站点档案-水箱信息详细信息
     * vn:villageNumber
     */
    @PreAuthorize("@ss.hasPermi('archive:waterbox:query')")
    @GetMapping(value = "/waterbox/v2/{vn}")
    @ApiOperation("获取水箱信息详细信息")
    public AjaxResult getInfo(@PathVariable("vn") String vn)
    {
        return success(waterTankInfoService.selectWaterTankInfoByVn(vn));
    }

    /**
     * 新增站点档案-水箱信息
     */
    @PreAuthorize("@ss.hasPermi('archive:waterbox:add')")
    @PostMapping(value = "/waterbox")
    @ApiOperation("新增水箱信息")
    public AjaxResult add(@RequestBody WaterTankInfo waterTankInfo)
    {
        return toAjax(waterTankInfoService.insertWaterTankInfo(waterTankInfo));
    }

    /**
     * 修改站点档案-水箱信息
     */
    @PreAuthorize("@ss.hasPermi('archive:waterbox:edit')")
    @PutMapping(value = "/waterbox")
    @ApiOperation("修改水箱信息")
    public AjaxResult edit(@RequestBody WaterTankInfo waterTankInfo)
    {
        return toAjax(waterTankInfoService.updateWaterTankInfo(waterTankInfo));
    }

    /**
     * 获取站点档案-加压设备信息详细信息
     */
//    @PreAuthorize("@ss.hasPermi('archive:pressureDevice:query')")
//    @GetMapping(value = "/pressureDevice/{id}")
//    @ApiOperation("获取加压设备信息详情")
//    public AjaxResult getPressureDeviceInfo(@PathVariable("id") Long id)
//    {
//        return success(pressureDevicesInfoService.selectPressureDeviceInfoById(id));
//        return success(pressureDevicesInfoService.selectPressureDeviceInfoByVillageAndArea(villageNumber, areaType));
//    }

    /**
     * 根据小区ID和区域类型获取加压设备信息
     */
    @PreAuthorize("@ss.hasPermi('archive:pressureDevicesInfo:query')")
    @GetMapping(value = "/pressureDevice/village/{villageNumber}/area/{areaType}")
    @ApiOperation("根据小区ID和区域类型获取加压设备信息")
    public AjaxResult getPressureDeviceInfoByVillageAndArea(@PathVariable("villageNumber") String villageNumber, @PathVariable("areaType") String areaType)
    {
        return success(pressureDevicesInfoService.selectPressureDevicesInfoByVillageAndArea(villageNumber, areaType));
    }

    /**
     * 新增站点档案-加压设备信息
     */
    @PreAuthorize("@ss.hasPermi('archive:pressureDevicesInfo:add')")
    @PostMapping(value = "/pressureDevice")
    @ApiOperation("添加加压设备信息")
    public AjaxResult addPressureDeviceInfo(@RequestBody Map<String, String> deviceInfoMap)
    {
        PressureDevicesInfo pressureDevicesInfo = JSONObject.parseObject(deviceInfoMap.get("deviceInfo"), PressureDevicesInfo.class);
        return toAjax(pressureDevicesInfoService.insertPressureDevicesInfo(pressureDevicesInfo));
    }

    /**
     * 修改站点档案-加压设备信息
     */
    @PreAuthorize("@ss.hasPermi('archive:pressureDevicesInfo:edit')")
    @PutMapping(value = "/pressureDevice")
    @ApiOperation("修改加压设备信息")
    public AjaxResult updatePressureDeviceInfo(@RequestBody Map<String, String> deviceInfoMap)
    {
        PressureDevicesInfo pressureDeviceInfo = JSONObject.parseObject(deviceInfoMap.get("deviceInfo"), PressureDevicesInfo.class);
        return toAjax(pressureDevicesInfoService.updatePressureDevicesInfo(pressureDeviceInfo));
    }

//    /**
//     * 删除站点档案-加压设备信息
//     */
//    @PreAuthorize("@ss.hasPermi('archive:pressureDevice:remove')")
//    @DeleteMapping(value = "/pressureDevice/{id}")
//    @ApiOperation("删除加压设备信息")
//    public AjaxResult deletePressureDeviceInfo(@PathVariable("id") Long id)
//    {
//        return toAjax(pressureDeviceInfoService.deletePressureDeviceInfoById(id));
//    }

//    /**
//     * 删除站点档案-加压设备信息
//     */
//    @PreAuthorize("@ss.hasPermi('archive:pressureDevice:remove')")
//    @DeleteMapping(value = "/pressureDevice/{id}")
//    @ApiOperation("删除加压设备信息")
//    public AjaxResult deletePressureDeviceInfo(@PathVariable("id") Long id)
//    {
//        return toAjax(pressureDevicesInfoService.deletePressureDeviceInfoById(id));
//    }

    /**
     * 查询站点档案-加压设备-其他设备信息列表
     */
    @PreAuthorize("@ss.hasPermi('aichive:otherDevs:list')")
    @GetMapping("/otherDevs/list")
    @ApiOperation("查询其他设备信息列表")
    public TableDataInfo otherDevslist(OtherDevices otherDevices)
    {
        startPage();
        List<OtherDevices> list = otherDevicesService.selectOtherDevicesList(otherDevices);
        return getDataTable(list);
    }

    /**
     * 获取站点档案-加压设备-其他设备信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('aichive:otherDevs:query')")
    @GetMapping(value = "/otherDevs/{id}")
    @ApiOperation("查询其他设备信息详细")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(otherDevicesService.selectOtherDevicesById(id));
    }

    /**
     * 新增站点档案-其他设备信息
     */
    @PreAuthorize("@ss.hasPermi('aichive:otherDevs:add')")
    @ApiOperation("新增其他设备信息")
    @PostMapping(value = "/otherDevs")
    public AjaxResult addOtherDevs(@RequestBody Map<String, String> otherDevicesMap)
    {

        return toAjax(otherDevicesService.insertOtherDevicesByBatch(otherDevicesMap));
    }

    /**
     * 修改站点档案-其他设备信息
     */
    @PreAuthorize("@ss.hasPermi('aichive:otherDevs:edit')")
    @ApiOperation("修改其他设备信息")
    @PutMapping(value = "/otherDevs")
    public AjaxResult editOtherDevs(@RequestBody OtherDevices otherDevices)
    {
        return toAjax(otherDevicesService.updateOtherDevices(otherDevices));
    }

    /**
     * 删除站点档案-其他设备信息
     */
    @PreAuthorize("@ss.hasPermi('aichive:otherDevs:remove')")
    @ApiOperation("删除其他设备信息")
    @DeleteMapping("/otherDevs/{ids}")
    public AjaxResult removeOtherDevs(@PathVariable Long[] ids)
    {
        return toAjax(otherDevicesService.deleteOtherDevicesByIds(ids));
    }

    /**
     * 导出站点档案信息Excel模板
     */
    @PreAuthorize("@ss.hasPermi('archive:template:export')")
    @GetMapping("/template/export")
    @ApiOperation("导出站点档案信息Excel模板")
    public void exportArchiveTemplate(HttpServletResponse response) throws IOException
    {
        excelTemplateService.exportArchiveTemplate(response);
    }

    /**
     * 导入站点档案信息
     */
    @PreAuthorize("@ss.hasPermi('archive:data:import')")
    @PostMapping("/import")
    @ApiOperation("导入站点档案信息")
    public AjaxResult importArchiveData(@RequestParam("file") MultipartFile file,
                                       @RequestParam("productId") Long productId) throws IOException
    {
        return excelTemplateService.importArchiveData(file, productId);
    }

}
