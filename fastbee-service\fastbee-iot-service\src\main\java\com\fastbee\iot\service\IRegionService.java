package com.fastbee.iot.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fastbee.iot.domain.Region;

import java.util.List;
import java.util.Map;

/**
 * @description TODO (/区域管理)
 * <AUTHOR>
 * @date 2025/1/3
 */
public interface IRegionService extends IService<Region> {

    /**
     * @description TODO (区域管理/区域管理列表)
     * @params [params]
     * @return com.basic.common.core.page.TableDataInfo
     * <AUTHOR>
     * @date 2025/1/3 13:55
     * @remark (改代码时写明修改时间修改人修改原因)
     */
    public List<Map<String, Object>> listRegion(Map<String, Object> params);

    /**
     * @description TODO (区域管理/删除区域管理)
     * @params [id]
     * @return com.basic.common.core.domain.AjaxResult
     * <AUTHOR>
     * @date 2025/1/3 14:46
     * @remark (改代码时写明修改时间修改人修改原因)
     */
    public int delRegion(String id);

    /**
     * @description TODO (区域管理/判断是否有子节点)
     * @params [id]
     * @return int
     * <AUTHOR>
     * @date 2025/1/3 14:51
     * @remark (改代码时写明修改时间修改人修改原因)
     */
    public boolean hasChildIds(String id);

    /**
     * @description TODO (区域管理/物资类型选择 列表)
     * @params [params]
     * @return java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     * <AUTHOR>
     * @date 2025/1/3 14:46
     * @remark (改代码时写明修改时间修改人修改原因)
     */
    public List<Map<String, Object>> optionList(Map<String, Object> params);

    List<Map<String, Object>> optionOperationCompanyList(Map<String, Object> params);

    List<Map<String, Object>> optionOperationCompanyUserList(Map<String, Object> params);

    /**
     * @description TODO (区域管理/物资类型选择 列表)
     * @params [params]
     * @return java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     * <AUTHOR>
     * @date 2025/1/3 14:46
     * @remark (改代码时写明修改时间修改人修改原因)
     */
    List<Map<String, Object>> optionEnterpriseList(Map<String, Object> params);

    /**
     * @description TODO (区域管理/物资类型选择 列表)
     * @params [params]
     * @return java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     * <AUTHOR>
     * @date 2025/1/3 14:46
     * @remark (改代码时写明修改时间修改人修改原因)
     */
    List<Map<String, Object>> optionDispatchList(Map<String, Object> params);

    /**
     * @description TODO (区域管理/物资类型选择 列表)
     * @params [params]
     * @return java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     * <AUTHOR>
     * @date 2025/1/3 14:46
     * @remark (改代码时写明修改时间修改人修改原因)
     */
    List<Map<String, Object>> optionCommunityList(Map<String, Object> params);


    List<Map<String, Object>> optionCommunityCountList(Map<String, Object> params);

    List<Map<String, Object>> optionCommunityCountListByLl(Map<String, Object> params);

    /**
     * @description 获取市级管理人员的区域列表
     * @params [params]
     * @return java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     * <AUTHOR>
     * @date 2025/2/12
     */
    List<Map<String, Object>> optionRegionListForCityManager(Map<String, Object> params);

    List<Map<String, Object>> getChildRegionsByParentId(String parentId);

    //查询当前节点的所有子节点
    List<String> getAllChildRegionIds(String parentId);

    //区域区级列表
    List<Map<String, Object>> listRegionByArea(Map<String, Object> params);

    //获取叶子节点区域列表
    List<Map<String, Object>> getLeafRegions();

    /**
     * 根据区域名称查询区域ID
     * @param regionName 区域名称
     * @return 区域ID，如果找不到返回null
     */
    Long getRegionIdByName(String regionName);
}
