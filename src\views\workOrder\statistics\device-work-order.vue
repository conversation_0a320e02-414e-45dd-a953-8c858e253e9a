<template>
  <div style="height: calc(100vh - 10px);">
    <!-- 绑定 @tab-change 事件 -->
    <el-tabs type="border-card" v-model="activeName" @tab-click="handleTabChange">
      <el-tab-pane label="告警工单" name="first" key="first">
        <!-- 添加 ref -->
        <AlarmWorkOrder ref="alarmWorkOrderRef" :device-id="deviceId" :village-id="villageId" ></AlarmWorkOrder>
      </el-tab-pane>
      <el-tab-pane label="手动工单" name="second" key="second">
        <HandleForm ref="handleFormRef" :device-id="deviceId" :village-id="villageId"></HandleForm>
      </el-tab-pane>
      <el-tab-pane label="线上巡检工单" name="third" key="third">
        <KeepFormPi ref="keepFormPiRef" :device-id="deviceId" :village-id="villageId" ></KeepFormPi>
      </el-tab-pane>
      <el-tab-pane label="线下巡检工单" name="fourth" key="fourth">
        <KeepFormOff ref="keepFormOffRef" :device-id="deviceId" :village-id="villageId" ></KeepFormOff>
      </el-tab-pane>
      <el-tab-pane label="定期工单" name="fifth" key="fifth">
        <KeepForm ref="keepFormRef" :device-id="deviceId" :village-id="villageId" ></KeepForm>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
export default {
  name: "DeviceWorkOrder",
  components: {
    AlarmWorkOrder: () => import('@/views/workOrder/statistics/alarmWorkOrder.vue'),
    HandleForm: () => import('@/views/workOrder/statistics/handleForm.vue'),
    KeepFormPi: () => import('@/views/workOrder/statistics/keepFormPi.vue'),
    KeepFormOff: () => import('@/views/workOrder/statistics/keepFormOff.vue'),
    KeepForm: () => import('@/views/workOrder/statistics/keepForm.vue'),
  },
  props: {
    deviceId: {
      type: String,
      default: ''
    },
    villageId: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      activeName: 'first'
    };
  },
  watch: {
    '$route'(to) {
      // 从路由参数中恢复标签状态
      if (to.query.activeTab) {
        this.activeName = to.query.activeTab;
      }
    },
    // 监听props变化，重新加载数据
    deviceId: {
      handler(newVal, oldVal) {
        if (newVal !== oldVal) {
          this.reloadCurrentTabData();
        }
      },
      immediate: false
    },
    villageId: {
      handler(newVal, oldVal) {
        if (newVal !== oldVal) {
          this.reloadCurrentTabData();
        }
      },
      immediate: false
    }
  },
  created() {
    // 初始化时从路由参数获取 activeTab
    if (this.$route.query.activeTab) {
      this.activeName = this.$route.query.activeTab;
    }
  },
  mounted() {
    // 组件挂载后加载当前标签页数据
    this.reloadCurrentTabData();
  },
  methods: {
    handleTabChange(tab) {
      // const activeName = tab.name; // 从 tab 对象中获取 name

      // 重新加载数据
      // const refMap = {
      //   first: 'alarmWorkOrderRef',
      //   second: 'handleFormRef',
      //   third: 'keepFormPiRef',
      //   fourth: 'keepFormOffRef',
      //   fifth: 'keepFormRef'
      // };
      //
      // const refName = refMap[activeName];
      // if (this.$refs[refName]?.reloadData) {
      //   this.$refs[refName].reloadData();
      // }
    },

    // 重新加载当前标签页数据
    reloadCurrentTabData() {
      // this.$nextTick(() => {
      //   const refMap = {
      //     first: 'alarmWorkOrderRef',
      //     second: 'handleFormRef',
      //     third: 'keepFormPiRef',
      //     fourth: 'keepFormOffRef',
      //     fifth: 'keepFormRef'
      //   };
      //
      //   const refName = refMap[this.activeName];
      //   if (this.$refs[refName]?.reloadData) {
      //     this.$refs[refName].reloadData();
      //   }
      // });
    }
  }
};
</script>

<style scoped>
.el-tabs {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.el-tabs__content {
  flex: 1;
  overflow: auto;
}

/* 确保分页组件可见 */
::v-deep .el-pagination {
  margin-top: 10px;
  z-index: 100;
  background-color: white;
  padding: 5px 0;
}
</style>
