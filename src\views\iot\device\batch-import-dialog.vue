<template>
    <!-- 批量导入设备 -->
    <el-dialog :title="upload.title" :visible.sync="upload.importDeviceDialog" width="550px" append-to-body>
        <div style="margin-top: -55px">
            <el-divider style="margin-top: -30px"></el-divider>
            <el-form label-position="top" :model="importForm" ref="importForm" :rules="importRules">
                <el-form-item label="产品" prop="productId">
                    <el-select v-model="importForm.productId" placeholder="请选择产品" style="width: 100%" filterable>
                        <el-option v-for="item in productList" :key="item.value" :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="上传文件" prop="fileList">
                    <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
                        v-model="importForm.fileList" action=""
                        :disabled="upload.isUploading" :on-progress="handleFileUploadProgress"
                        :on-success="handleFileSuccess" :auto-upload="false" :on-change="handleChange"
                        :on-remove="handleRemove" :http-request="handleImport" drag>
                        <i class="el-icon-upload"></i>
                        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                        <div class="el-upload__tip" slot="tip">
                            <div style="margin-top: 10px;">
                                <span>提示:仅允许导入xls、xlsx格式文件。</span>
                            </div>
                        </div>
                    </el-upload>
                    <el-link type="primary" :underline="false" style="font-size:14px;vertical-align: baseline;"
                        @click="importTemplate"><i class="el-icon-download"></i>下载站点导入模板</el-link>
                </el-form-item>
            </el-form>
        </div>
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitFileForm">确 定</el-button>
            <el-button @click="upload.importDeviceDialog = false">取 消</el-button>
        </div>
    </el-dialog>
</template>
<script>
import { listProduct } from "@/api/iot/product";
import { getToken } from "@/utils/auth";
import { exportArchiveTemplate, importArchiveData } from '@/api/archive/pumpInfo';
export default {
    name: 'batchImport',
    data() {
        return {
            type: 1,
            //导入表单
            importForm: {
                productId: null,
                fileList: [],
            },
            productList: [],
            file: null,
            // 批量导入参数
            upload: {
                // 是否显示弹出层
                importDeviceDialog: false,
                // 弹出层标题
                title: "批量导入",
                // 是否禁用上传
                isUploading: false,
                // 设置上传的请求头部
                headers: { Authorization: "Bearer " + getToken() },
                // 上传的地址
                url: process.env.VUE_APP_BASE_API + "/iot/device/importData"
            },
            // 批量导入表单校验
            importRules: {
                productId: [{ required: true, message: '产品不能为空', trigger: 'change' }],
                fileList: [
                    { required: true, message: '请上传文件', trigger: 'change' }
                ]
            },
        };
    },
    created() {
        this.getProductList();
    },
    methods: {
        /** 下载模板操作 */
        async importTemplate() {
            try {
                this.$modal.loading("正在导出模板，请稍候...");
                const response = await exportArchiveTemplate();

                // 创建下载链接
                const blob = new Blob([response], {
                    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                });
                const url = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = '站点档案信息导入模板.xlsx';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                window.URL.revokeObjectURL(url);

                this.$modal.closeLoading();
                this.$modal.msgSuccess("模板导出成功");
            } catch (error) {
                this.$modal.closeLoading();
                this.$modal.msgError("模板导出失败：" + error.message);
            }
        },
        // 选择文件后给表单验证的prop字段赋值， 并且清除该字段的校验
        handleChange(file, fileList) {
            this.importForm.fileList = fileList;
            // 防止用户打开了文件选择框之后不选择文件而出现效验失败
            if (this.importForm.fileList) {
                this.$refs.importForm.clearValidate('fileList');
            }
        },
        // 删除文件后重新校验该字段
        handleRemove(file, fileList) {
            this.importForm.fileList = fileList;
            this.$refs.importForm.validateField('fileList');
        },
        // 文件上传中处理
        handleFileUploadProgress(event, file, fileList) {
            this.upload.isUploading = true;
        },
        // 自定义上传方法
        async handleImport(options) {
            if (!this.importForm.productId) {
                this.$modal.msgError("请先选择产品");
                return;
            }

            const formData = new FormData();
            formData.append('file', options.file);

            try {
                this.$modal.loading("正在导入数据，请稍候...");
                const response = await importArchiveData(formData, this.importForm.productId);
                this.$modal.closeLoading();

                if (response.code === 200) {
                    this.$modal.msgSuccess("数据导入成功");
                    this.upload.importDeviceDialog = false;
                    this.$refs.upload.clearFiles();
                    // 通知父组件刷新数据
                    this.$emit('save');
                } else {
                    this.$modal.msgError("数据导入失败：" + response.msg);
                }
            } catch (error) {
                this.$modal.closeLoading();
                this.$modal.msgError("数据导入失败：" + error.message);
            }
        },
        // 文件上传成功处理
        handleFileSuccess(response, file, fileList) {
            this.$modal.msgSuccess("文件上传成功");
        },
        /** 查询产品列表 */
        getProductList() {
            this.loading = true;
            const params = {
                pageSize: 999,
            }
            listProduct(params).then(response => {
                this.productList = response.rows.map((item) => {
                    return { value: item.productId, label: item.productName };
                });
                this.total = response.total;
                this.loading = false;
            });
        },
        // 提交上传文件
        submitFileForm() {
            this.$refs['importForm'].validate((valid) => {
                if (valid) {
                    this.$refs.upload.submit();
                }
            });
        },
    },
};
</script>
