package com.fastbee.iot.service.impl;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fastbee.common.core.domain.entity.SysRole;
import com.fastbee.common.core.domain.entity.SysUser;
import com.fastbee.common.utils.TreeUtils;
import com.fastbee.iot.domain.Region;
import com.fastbee.iot.mapper.RegionMapper;
import com.fastbee.iot.service.IRegionService;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.configurationprocessor.json.JSONArray;
import org.springframework.boot.configurationprocessor.json.JSONException;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.fastbee.common.utils.SecurityUtils.getLoginUser;

/**
 * @description TODO (/区域管理)
 * <AUTHOR>
 * @date 2025/1/3
 */
@Service
public class RegionServiceImpl extends ServiceImpl<RegionMapper, Region> implements IRegionService {
    @Autowired
    private RegionMapper regionMapper;

    /**
     * @description TODO (区域管理/区域管理列表)
     * @params [params]
     * @return com.basic.common.core.page.TableDataInfo
     * <AUTHOR>
     * @date 2025/1/3 13:55
     * @remark (改代码时写明修改时间修改人修改原因)
     */
    @Override
    public List<Map<String, Object>> listRegion(Map<String, Object> params){
        SysUser user = getLoginUser().getUser();
        Long regionId = user.getRegionId();
        if(null != regionId){
            List<String> childRegions = getAllChildRegionIds(regionId.toString());
            if(childRegions.size() >0){
                List<Long> longIds = new ArrayList<>();
                for (String id : childRegions) {
                    try {
                        longIds.add(Long.parseLong(id));
                    } catch (NumberFormatException e) {
                        // 处理转换异常，这里简单打印异常信息，可根据实际情况调整
                        System.err.println("无法将字符串 " + id + " 转换为 Long 类型: " + e.getMessage());
                    }
                }
                try {
                    String[] regionFullPathArr = getRegionFullPathById(regionId.toString());
                    for (String id : regionFullPathArr) {
                        if(id != "0"){
                            longIds.add(Long.parseLong(id));
                        }
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                longIds.add(regionId);
                params.put("regionId",null);
                params.put("regionIds",longIds);
            }
        }

        List<Map<String, Object>> list = regionMapper.listRegion(params);
        return list;
    }

    /**
     * @description TODO (区域管理/删除区域管理)
     * @params [id]
     * @return com.basic.common.core.domain.AjaxResult
     * <AUTHOR>
     * @date 2025/1/3 14:46
     * @remark (改代码时写明修改时间修改人修改原因)
     */
    @Override
    public int delRegion(String id){
        return regionMapper.delRegion(id);
    }

    /**
     * @description TODO (区域管理/判断是否有子节点)
     * @params [id]
     * @return int
     * <AUTHOR>
     * @date 2025/1/3 14:51
     * @remark (改代码时写明修改时间修改人修改原因)
     */
    @Override
    public boolean hasChildIds(String id) {
        int result = regionMapper.hasChildIds(id);
        return result > 0;
    }

    /**
     * @description TODO (区域管理/物资类型选择 列表)
     * @params [params]
     * @return java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     * <AUTHOR>
     * @date 2025/1/3 14:46
     * @remark (改代码时写明修改时间修改人修改原因)
     */
    @Override
    public List<Map<String, Object>> optionList(Map<String, Object> params){
        SysUser user = getLoginUser().getUser();
        Long regionId = user.getRegionId();
        if(null != regionId){
            List<String> childRegions = getAllChildRegionIds(regionId.toString());
            if(childRegions.size() >0){
                List<Long> longIds = new ArrayList<>();
                for (String id : childRegions) {
                    try {
                        longIds.add(Long.parseLong(id));
                    } catch (NumberFormatException e) {
                        // 处理转换异常，这里简单打印异常信息，可根据实际情况调整
                        System.err.println("无法将字符串 " + id + " 转换为 Long 类型: " + e.getMessage());
                    }
                }
                try {
                    String[] regionFullPathArr = getRegionFullPathById(regionId.toString());
                    for (String id : regionFullPathArr) {
                        if(id != "0"){
                            longIds.add(Long.parseLong(id));
                        }
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                longIds.add(regionId);
                params.put("regionId",null);
                params.put("regionIds",longIds);
            }
        }
        List<Map<String, Object>> list = regionMapper.optionList(params);
        List<Map<String, Object>> nodes = TreeUtils.buildTree(list);
        return nodes;
    }

    @Override
    public List<Map<String, Object>> optionOperationCompanyList(Map<String, Object> params){
        List<Map<String, Object>> list = regionMapper.optionOperationCompanyList(params);
//        List<Map<String, Object>> nodes = TreeUtils.buildTree(list);
        return list;
    }

    @Override
    public List<Map<String, Object>> optionOperationCompanyUserList(Map<String, Object> params){
        SysUser user = getLoginUser().getUser();
        Long regionId = user.getRegionId();
        params.put("userRegionId",regionId);//查询当前区运维公司
        if(null != regionId){
            List<String> childRegions = getAllChildRegionIds(regionId.toString());
            if(childRegions.size() >0){
                List<Long> longIds = new ArrayList<>();
                for (String id : childRegions) {
                    try {
                        longIds.add(Long.parseLong(id));
                    } catch (NumberFormatException e) {
                        // 处理转换异常，这里简单打印异常信息，可根据实际情况调整
                        System.err.println("无法将字符串 " + id + " 转换为 Long 类型: " + e.getMessage());
                    }
                }
                try {
                    String[] regionFullPathArr = getRegionFullPathById(regionId.toString());
                    for (String id : regionFullPathArr) {
                        if(id != "0"){
                            longIds.add(Long.parseLong(id));
                        }
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                longIds.add(regionId);
                params.put("regionId",null);
                params.put("regionIds",longIds);
            }
        }
        List<Map<String, Object>> list = regionMapper.optionOperationCompanyUserList(params);
//        List<Map<String, Object>> nodes = TreeUtils.buildTree(list);
        return list;
    }

    @Override
    public List<Map<String, Object>> optionEnterpriseList(Map<String, Object> params) {
        SysUser user = getLoginUser().getUser();
        Long regionId = user.getRegionId();
        if(null != regionId){
            List<String> childRegions = getAllChildRegionIds(regionId.toString());
            System.err.println("===="+childRegions);
            if(childRegions.size() >0){
                List<Long> longIds = new ArrayList<>();
                for (String id : childRegions) {
                    try {
                        longIds.add(Long.parseLong(id));
                    } catch (NumberFormatException e) {
                        // 处理转换异常，这里简单打印异常信息，可根据实际情况调整
                        System.err.println("无法将字符串 " + id + " 转换为 Long 类型: " + e.getMessage());
                    }
                }
                try {
                    String[] regionFullPathArr = getRegionFullPathById(regionId.toString());
                    for (String id : regionFullPathArr) {
                        if(id != "0"){
                            longIds.add(Long.parseLong(id));
                        }
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                longIds.add(regionId);
                params.put("regionId",null);
                params.put("regionIds",longIds);
            }
        }
        List<Map<String, Object>> list = regionMapper.optionEnterpriseList(params);
        List<Map<String, Object>> nodes = TreeUtils.buildTree(list);
        return nodes;
    }

    @Override
    public List<Map<String, Object>> optionDispatchList(Map<String, Object> params) {
        List<Map<String, Object>> list = regionMapper.optionDispatchList(params);
        List<Map<String, Object>> nodes = TreeUtils.buildTree(list);
        return nodes;
    }

    @Override
    public List<Map<String, Object>> optionCommunityList(Map<String, Object> params) {
        SysUser user = getLoginUser().getUser();
        List<SysRole> roles = user.getRoles();
        Long regionId = user.getRegionId();
        for (int i = 0; i < roles.size(); i++) {
            if(roles.get(i).getRoleKey().equals("community")){/*小区管理员*/
                params.put("adminId",user.getUserId());
            }
        }
        if(null != regionId){
            List<String> childRegions = getAllChildRegionIds(regionId.toString());
            if(childRegions.size() >0){
                List<Long> longIds = new ArrayList<>();
                for (String id : childRegions) {
                    try {
                        longIds.add(Long.parseLong(id));
                    } catch (NumberFormatException e) {
                        // 处理转换异常，这里简单打印异常信息，可根据实际情况调整
                        System.err.println("无法将字符串 " + id + " 转换为 Long 类型: " + e.getMessage());
                    }
                }
                try {
                    String[] regionFullPathArr = getRegionFullPathById(regionId.toString());
                    for (String id : regionFullPathArr) {
                        if(id != "0"){
                            longIds.add(Long.parseLong(id));
                        }
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                longIds.add(regionId);
                params.put("regionId",null);
                params.put("regionIds",longIds);
            }
        }
        List<Map<String, Object>> list = regionMapper.optionCommunityList(params);
        List<Map<String, Object>> nodes = TreeUtils.buildTree(list);
        return nodes;
    }

    @Override
    public List<Map<String, Object>> optionCommunityCountList(Map<String, Object> params) {
        SysUser user = getLoginUser().getUser();
        List<SysRole> roles = user.getRoles();
        Long regionId = user.getRegionId();
        for (int i = 0; i < roles.size(); i++) {
            if(roles.get(i).getRoleKey().equals("community")){/*小区管理员*/
                params.put("adminId",user.getUserId());
            }
        }
        if(null != regionId){
            List<String> childRegions = getAllChildRegionIds(regionId.toString());
            if(childRegions.size() >0){
                List<Long> longIds = new ArrayList<>();
                for (String id : childRegions) {
                    try {
                        longIds.add(Long.parseLong(id));
                    } catch (NumberFormatException e) {
                        // 处理转换异常，这里简单打印异常信息，可根据实际情况调整
                        System.err.println("无法将字符串 " + id + " 转换为 Long 类型: " + e.getMessage());
                    }
                }
                try {
                    String[] regionFullPathArr = getRegionFullPathById(regionId.toString());
                    for (String id : regionFullPathArr) {
                        if(id != "0"){
                            longIds.add(Long.parseLong(id));
                        }
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                longIds.add(regionId);
                params.put("regionId",null);
                params.put("regionIds",longIds);
            }
        }
        List<Map<String, Object>> list = regionMapper.optionCommunityCountList(params);
        /*添加高区，中区、低区*/
        List<Map<String, Object>> finalResults = addVillageChild(list, 0);
        List<Map<String, Object>> nodes = TreeUtils.buildTree(finalResults);
        return nodes;
    }

    @Override
    public List<Map<String, Object>> optionCommunityCountListByLl(Map<String, Object> params) {
        SysUser user = getLoginUser().getUser();
        List<SysRole> roles = user.getRoles();
        Long regionId = user.getRegionId();
        for (int i = 0; i < roles.size(); i++) {
            if(roles.get(i).getRoleKey().equals("community")){/*小区管理员*/
                params.put("adminId",user.getUserId());
            }
        }
        if(null != regionId){
            List<String> childRegions = getAllChildRegionIds(regionId.toString());
            if(childRegions.size() >0){
                List<Long> longIds = new ArrayList<>();
                for (String id : childRegions) {
                    try {
                        longIds.add(Long.parseLong(id));
                    } catch (NumberFormatException e) {
                        // 处理转换异常，这里简单打印异常信息，可根据实际情况调整
                        System.err.println("无法将字符串 " + id + " 转换为 Long 类型: " + e.getMessage());
                    }
                }
                try {
                    String[] regionFullPathArr = getRegionFullPathById(regionId.toString());
                    for (String id : regionFullPathArr) {
                        if(id != "0"){
                            longIds.add(Long.parseLong(id));
                        }
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                longIds.add(regionId);
                params.put("regionId",null);
                params.put("regionIds",longIds);
            }
        }
        List<Map<String, Object>> list = regionMapper.optionCommunityCountList(params);
        /*添加高区，中区、低区*/
        List<Map<String, Object>> finalResults = addVillageChild(list, 1);
        List<Map<String, Object>> nodes = TreeUtils.buildTree(finalResults);
        return nodes;
    }


    public List<Map<String,Object>> addVillageChild(List<Map<String,Object>> originalResults ,Integer type){
        // 创建一个新的列表来存储最终结果
        List<Map<String, Object>> finalResults = new ArrayList<>();
        // 遍历原始结果
        for (Map<String, Object> row : originalResults) {
            // 将原始行添加到最终结果中

            finalResults.add(row);
            // 检查 deviceId 是否不为空
            if (row.get("deviceId") != null && row.get("serialNumber") != null) {
                // 获取父节点的 id 和 name
                Long parentId = Long.valueOf(row.get("id").toString());
                String villageName = (String) row.get("name");
                String serialNumber = (String) row.get("serialNumber");
                serialNumber = serialNumber.toUpperCase();
                row.put("serialNumber",serialNumber);
                // 创建新的 "高区" 子节点
                Map<String, Object> highZoneNode = createChildNode(parentId, "高区", generateNewId(parentId,"高区"),null,villageName,null);
                finalResults.add(highZoneNode);
                addSubNodes(type, finalResults, highZoneNode, "高区",serialNumber);

                // 创建新的 "中区" 子节点
                Map<String, Object> midZoneNode = createChildNode(parentId, "中区", generateNewId(parentId,"中区"),null,villageName,null);
                finalResults.add(midZoneNode);
                addSubNodes(type, finalResults, midZoneNode, "中区",serialNumber);

                // 创建新的 "低区" 子节点
                Map<String, Object> lowZoneNode = createChildNode(parentId, "低区", generateNewId(parentId,"低区"),null,villageName,null);
                finalResults.add(lowZoneNode);
                addSubNodes(type, finalResults, lowZoneNode, "低区",serialNumber);

            }

        }

        return finalResults;
    }

    // 创建新的子节点
    private Map<String, Object> createChildNode(Long parentId, String name, Long newId,String identity,String villageName,String serialNumber) {
        Map<String, Object> childNode = new HashMap<>();
        childNode.put("id", newId);
        childNode.put("name", name);
        childNode.put("parentId", parentId);
        childNode.put("deviceId", null);
        if(Strings.isNotEmpty(identity)){
            childNode.put("identity", identity);
        }
        if(Strings.isNotEmpty(villageName)){
            childNode.put("villageName", villageName);
        }
        if(Strings.isNotEmpty(serialNumber)){
            childNode.put("serialNumber", serialNumber);
        }
        if(Strings.isNotEmpty(serialNumber) && Strings.isNotEmpty(identity)){
            childNode.put("prop", serialNumber+"_"+identity);
        }
        return childNode;
    }

    // 为指定节点添加子节点（优化后）
    private void addSubNodes(Integer type, List<Map<String, Object>> finalResults,
                             Map<String, Object> parentNode, String prefix,String serialNumber) {
        // 参数校验
        Objects.requireNonNull(parentNode, "父节点不能为空");
        Objects.requireNonNull(prefix, "名称前缀不能为空");

        Long parentId = (Long) parentNode.get("id");
        String[] subNodeNames = null;
        if(type == 0){
            subNodeNames = new String[]{prefix + "出口压力", prefix + "设定压力"};
        }else{
            subNodeNames = new String[]{prefix + "累计", prefix + "瞬时"};
        }
        String idenPre = "";
        switch (prefix) {
            case "高区":
                idenPre = "High";
                break;
            case "中区":
                idenPre = "Median";
                break;
            case "低区":
                idenPre = "Low";
                break;
            default:
                break;
        }

        String[] identityArr = {idenPre + "OutPressure", idenPre + "SetPressure",
                idenPre + "TotalFlow", idenPre + "FlowNow"};

        for (int i = 0; i < subNodeNames.length; i++) {
            Long subId = generateNewId(parentId, prefix) + i + 1;
            // 检查ID唯一性
            boolean exists = finalResults.stream()
                    .anyMatch(node -> subId.equals(node.get("id")));
            if (!exists) {
                Map<String, Object> subNode = createChildNode(parentId, subNodeNames[i], subId ,identityArr[i],null,serialNumber);
                finalResults.add(subNode);
            }
        }
    }

    // 生成新ID（优化后）
    private Long generateNewId(Long parentId, String zoneType) {
        Objects.requireNonNull(zoneType, "zoneType 不能为空");
        int base;
        switch (zoneType) {
            case "高区": base = 1000; break;
            case "中区": base = 2000; break;
            case "低区": base = 3000; break;
            default: throw new IllegalArgumentException("未知区域类型: " + zoneType);
        }
        return parentId * 10000L + base; // 显式长整型运算
    }

    @Override
    public List<Map<String, Object>> optionRegionListForCityManager(Map<String, Object> params){
        SysUser user = getLoginUser().getUser();
        Long regionId = user.getRegionId();
        if(null != regionId){
            List<String> childRegions = getAllChildRegionIds(regionId.toString());
            if(childRegions.size() >0){
                List<Long> longIds = new ArrayList<>();
                for (String id : childRegions) {
                    try {
                        longIds.add(Long.parseLong(id));
                    } catch (NumberFormatException e) {
                        // 处理转换异常，这里简单打印异常信息，可根据实际情况调整
                        System.err.println("无法将字符串 " + id + " 转换为 Long 类型: " + e.getMessage());
                    }
                }
                try {
                    String[] regionFullPathArr = getRegionFullPathById(regionId.toString());
                    for (String id : regionFullPathArr) {
                        if(id != "0"){
                            longIds.add(Long.parseLong(id));
                        }
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                longIds.add(regionId);
                params.put("regionId",null);
                params.put("regionIds",longIds);
            }
        }
        List<Map<String, Object>> list = regionMapper.optionRegionListForCityManager(params);
        List<Map<String, Object>> nodes = TreeUtils.buildTree(list);
        return nodes;
    }

    @Override
    public List<Map<String, Object>> getChildRegionsByParentId(String parentId) {
        return regionMapper.getChildRegionsByParentId(parentId);
    }


    public String[] getRegionFullPathById(String regionId) throws JSONException {
        Map<String, Object> regionMap = regionMapper.getRegionById(regionId);
        if(!regionMap.isEmpty()){
            String fullPath = regionMap.get("fullPath").toString();
            // 将字符串解析为JSONArray对象
            JSONArray jsonArray = new JSONArray(fullPath);

            // 遍历JSONArray并处理每个元素
            String[] split = new String[jsonArray.length()];
            for (int i = 0; i < jsonArray.length(); i++) {
                String element = jsonArray.getString(i);
                if(element!="0"){
                    split[i]= element;
                }

            }
            return split;
        }
        return null;
    }

    // 递归方法，获取所有子区域的 id
    public void findAllChildIds(String parentId, List<String> regionIds) {
        List<Map<String, Object>> childRegions = getChildRegionsByParentId(parentId);
        for (Map<String, Object> childRegion : childRegions) {
            regionIds.add(childRegion.get("id").toString());
            findAllChildIds(childRegion.get("id").toString(), regionIds); // 递归查找子区域
        }
    }

    public List<String> getAllChildRegionIds(String parentId) {
        List<String> regionIds = new ArrayList<>();
        findAllChildIds(parentId, regionIds);
        return regionIds;
    }

    @Override
    public List<Map<String, Object>> listRegionByArea(Map<String, Object> params){
        SysUser user = getLoginUser().getUser();
        Long regionId = user.getRegionId();
        if(null != regionId){
            List<String> childRegions = getAllChildRegionIds(regionId.toString());
            if(childRegions.size() >0){
                List<Long> longIds = new ArrayList<>();
                for (String id : childRegions) {
                    try {
                        longIds.add(Long.parseLong(id));
                    } catch (NumberFormatException e) {
                        // 处理转换异常，这里简单打印异常信息，可根据实际情况调整
                        System.err.println("无法将字符串 " + id + " 转换为 Long 类型: " + e.getMessage());
                    }
                }
                try {
                    String[] regionFullPathArr = getRegionFullPathById(regionId.toString());
                    for (String id : regionFullPathArr) {
                        if(id != "0"){
                            longIds.add(Long.parseLong(id));
                        }
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                longIds.add(regionId);
                params.put("regionId",null);
                params.put("regionIds",longIds);
            }
        }
        List<Map<String, Object>> list = regionMapper.listRegionByArea(params);
        return list;
    }

    @Override
    public List<Map<String, Object>> getLeafRegions() {
        // 获取所有区域
        List<Map<String, Object>> allRegions = regionMapper.selectAllRegions();
        List<Map<String, Object>> leafRegions = new ArrayList<>();

        // 找出叶子节点（没有子节点的区域）
        for (Map<String, Object> region : allRegions) {
            String regionId = region.get("id").toString();
            List<Map<String, Object>> children = regionMapper.getChildRegionsByParentId(regionId);

            // 如果没有子节点，则为叶子节点
            if (children == null || children.isEmpty()) {
                Map<String, Object> leafRegion = new HashMap<>();
                leafRegion.put("value", regionId);
                leafRegion.put("label", region.get("name"));
                leafRegion.put("fullPath", region.get("fullPath"));
                leafRegions.add(leafRegion);
            }
        }

        return leafRegions;
    }

    @Override
    public Long getRegionIdByName(String regionName) {
        if (regionName == null || regionName.trim().isEmpty()) {
            return null;
        }
        return regionMapper.getRegionIdByName(regionName.trim());
    }
}
