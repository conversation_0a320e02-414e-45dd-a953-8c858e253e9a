package com.fastbee.iot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fastbee.iot.domain.Region;

import java.util.List;
import java.util.Map;

/**
 * @description TODO (/区域管理)
 * <AUTHOR>
 * @date 2025/1/3
 */
public interface RegionMapper extends BaseMapper<Region> {

    /**
     * @description TODO (区域管理/区域管理列表)
     * @params [params]
     * @return com.basic.common.core.page.TableDataInfo
     * <AUTHOR>
     * @date 2025/1/3 13:55
     * @remark (改代码时写明修改时间修改人修改原因)
     */
    List<Map<String, Object>> listRegion(Map<String, Object> params);

    /**
     * @description TODO (区域管理/删除区域管理)
     * @params [id]
     * @return com.basic.common.core.domain.AjaxResult
     * <AUTHOR>
     * @date 2025/1/3 14:46
     * @remark (改代码时写明修改时间修改人修改原因)
     */
    int delRegion(String id);

    /**
     * @description TODO (区域管理/判断是否有子节点)
     * @params [id]
     * @return int
     * <AUTHOR>
     * @date 2025/1/3 14:51
     * @remark (改代码时写明修改时间修改人修改原因)
     */
    int hasChildIds(String id);

    /**
     * @description TODO (区域管理/物资类型选择 列表)
     * @params [params]
     * @return java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     * <AUTHOR>
     * @date 2025/1/3 14:46
     * @remark (改代码时写明修改时间修改人修改原因)
     */
    List<Map<String, Object>> optionList(Map<String, Object> params);

    List<Map<String, Object>> optionEnterpriseList(Map<String, Object> params);

    List<Map<String, Object>> optionOperationCompanyList(Map<String, Object> params);

    List<Map<String, Object>> optionOperationCompanyUserList(Map<String, Object> params);

    List<Map<String, Object>> optionDispatchList(Map<String, Object> params);

    List<Map<String, Object>> optionCommunityList(Map<String, Object> params);

    List<Map<String, Object>> optionCommunityCountList(Map<String, Object> params);

    /**
     * @description TODO (区域管理/物资类型选择 列表)
     * @params [params]
     * @return 市级管理人员的区域列表
     * <AUTHOR>
     * @date 2025/2/12
     * @remark (改代码时写明修改时间修改人修改原因)
     */
    List<Map<String, Object>> optionRegionListForCityManager(Map<String, Object> params);

    List<Map<String, Object>> getChildRegionsByParentId(String parentId);

    Map<String, Object> getRegionById(String regionId);

    List<Map<String, Object>> listRegionByArea(Map<String, Object> params);

    /**
     * 获取所有区域
     */
    List<Map<String, Object>> selectAllRegions();

    /**
     * 根据区域名称查询区域ID
     * @param regionName 区域名称
     * @return 区域ID，如果找不到返回null
     */
    Long getRegionIdByName(String regionName);
}
